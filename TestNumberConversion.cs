using System;
using DriverManagementSystem.Helpers;

namespace DriverManagementSystem
{
    /// <summary>
    /// اختبار دالة تحويل الأرقام إلى النص العربي
    /// </summary>
    public class TestNumberConversion
    {
        public static void Main()
        {
            Console.WriteLine("=== اختبار دالة تحويل الأرقام إلى النص العربي ===");
            Console.WriteLine();

            // اختبار الرقم 35200 كما في الصورة
            decimal testAmount = 35200.00m;
            
            Console.WriteLine($"الرقم: {testAmount:N2}");
            Console.WriteLine($"النص العربي: {NumberToArabicTextHelper.ConvertToArabicText(testAmount)}");
            Console.WriteLine($"النص العربي مع العملة: {NumberToArabicTextHelper.ConvertAmountToArabicText(testAmount)}");
            Console.WriteLine();

            // اختبارات إضافية
            Console.WriteLine("=== اختبارات إضافية ===");
            
            var testNumbers = new decimal[] { 
                1000, 2000, 5000, 10000, 15000, 20000, 25000, 30000, 35000, 35200, 40000, 50000, 100000 
            };

            foreach (var number in testNumbers)
            {
                Console.WriteLine($"{number:N0} = {NumberToArabicTextHelper.ConvertToArabicText(number)}");
            }

            Console.WriteLine();
            Console.WriteLine("=== اختبار مع العملة ===");
            Console.WriteLine($"35200 = {NumberToArabicTextHelper.ConvertAmountToArabicText(35200)}");
            
            Console.WriteLine();
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
