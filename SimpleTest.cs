using System;

public static class NumberToArabicTextHelper
{
    private static readonly string[] ones = {
        "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
        "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
    };

    private static readonly string[] tens = {
        "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
    };

    private static readonly string[] hundreds = {
        "", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة"
    };

    public static string ConvertToArabicText(decimal number)
    {
        try
        {
            if (number == 0)
                return "صفر";

            if (number < 0)
                return "سالب " + ConvertToArabicText(-number);

            long intNumber = (long)number;

            if (intNumber == 0)
                return "صفر";

            string result = "";

            // الآلاف
            if (intNumber >= 1000)
            {
                long thousands = intNumber / 1000;
                string thousandText = ConvertHundreds((int)thousands);
                if (!string.IsNullOrEmpty(thousandText))
                {
                    if (thousands == 1)
                        result += "ألف";
                    else if (thousands == 2)
                        result += "ألفان";
                    else if (thousands <= 10)
                        result += thousandText + " آلاف";
                    else
                        result += thousandText + " ألف";

                    intNumber %= 1000;
                    if (intNumber > 0) result += " و";
                }
            }

            // المئات والعشرات والآحاد
            if (intNumber > 0)
            {
                string remainderText = ConvertHundreds((int)intNumber);
                if (!string.IsNullOrEmpty(remainderText))
                {
                    result += remainderText;
                }
            }

            return result.Trim();
        }
        catch
        {
            return "خطأ في التحويل";
        }
    }

    private static string ConvertHundreds(int number)
    {
        if (number == 0) return "";

        string result = "";

        // المئات
        if (number >= 100)
        {
            int hundredsDigit = number / 100;
            result += hundreds[hundredsDigit];
            number %= 100;

            if (number > 0)
                result += " و";
        }

        // العشرات والآحاد
        if (number >= 20)
        {
            int tensDigit = number / 10;
            int onesDigit = number % 10;

            if (onesDigit > 0)
                result += ones[onesDigit] + " و" + tens[tensDigit];
            else
                result += tens[tensDigit];
        }
        else if (number > 0)
        {
            result += ones[number];
        }

        return result.Trim();
    }

    public static string ConvertAmountToArabicText(decimal amount)
    {
        try
        {
            if (amount == 0)
                return "صفر ريال يمني";

            string arabicText = ConvertToArabicText(amount);
            return arabicText + " ريال يمني";
        }
        catch
        {
            return "خطأ في تحويل المبلغ";
        }
    }
}

public class Program
{
    public static void Main()
    {
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        Console.WriteLine("=== اختبار دالة تحويل الأرقام إلى النص العربي ===");
        Console.WriteLine();

        // اختبار الرقم 35200 كما في الصورة
        decimal testAmount = 35200.00m;
        
        Console.WriteLine($"الرقم: {testAmount:N2}");
        Console.WriteLine($"النص العربي: {NumberToArabicTextHelper.ConvertToArabicText(testAmount)}");
        Console.WriteLine($"النص العربي مع العملة: {NumberToArabicTextHelper.ConvertAmountToArabicText(testAmount)}");
        Console.WriteLine();

        // اختبارات إضافية
        Console.WriteLine("=== اختبارات إضافية ===");
        
        var testNumbers = new decimal[] { 
            200, 1000, 2000, 5000, 10000, 15000, 20000, 25000, 30000, 35000, 35200, 40000, 50000, 100000 
        };

        foreach (var number in testNumbers)
        {
            Console.WriteLine($"{number:N0} = {NumberToArabicTextHelper.ConvertToArabicText(number)}");
        }

        Console.WriteLine();
        Console.WriteLine("اضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
}
