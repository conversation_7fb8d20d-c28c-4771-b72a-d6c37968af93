<Window x:Class="DriverManagementSystem.Views.ContractEditorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تحرير قالب العقد"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Colors.xaml"/>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📝" FontSize="32" Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <TextBlock Text="تحرير قالب العقد" 
                             FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="💾 حفظ التغييرات"
                            Click="SaveButton_Click"
                            Background="#28A745"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#218838"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="🔄 استعادة الافتراضي"
                            Click="ResetButton_Click"
                            Background="#FFC107"
                            Foreground="#333333"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#E0A800"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="💰 تحديث تنسيق المبالغ"
                            Click="UpdateAmountFormatButton_Click"
                            Background="#17A2B8"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#138496"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    <Button Content="❌ إغلاق"
                            Click="CloseButton_Click"
                            Background="#DC3545"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#C82333"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="20">
            <StackPanel>
                
                <!-- مقدمة العقد -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="📋 مقدمة العقد" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="ContractIntroductionTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="60"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- الطرف الأول -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="👤 الطرف الأول (مالك السيارة والسائق)" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="FirstPartyTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="60"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- الطرف الثاني -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="🏛️ الطرف الثاني (الصندوق الاجتماعي للتنمية)" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="SecondPartyTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- مواصفات السيارة -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="🚗 أولاً: مواصفات السيارة" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="VehicleSpecsTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- غرض الانتفاع -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="🎯 ثانياً: غرض الانتفاع" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="PurposeTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- المدة الإيجارية -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="📅 ثالثاً: المدة الإيجارية" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="DurationTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- القيمة الإيجارية -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="💰 رابعاً: القيمة الإيجارية" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="PriceTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- إقرار الملكية -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="📋 خامساً: إقرار الملكية" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="OwnershipTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

                <!-- التزامات الطرف الأول -->
                <Border Background="White" Padding="15" Margin="0,0,0,15" BorderBrush="#DDDDDD" BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="⚖️ سادساً: التزامات الطرف الأول" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBox Name="ObligationsTextBox"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="120"
                                FontSize="12"
                                Padding="10"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="15">
            <StackPanel>
                <TextBlock Text="💡 يمكنك استخدام المتغيرات التالية وسيتم استبدالها تلقائياً بالبيانات الفعلية:"
                         FontSize="12" FontWeight="Bold" Foreground="#495057" Margin="0,0,0,5"/>
                <TextBlock FontSize="11" Foreground="#6C757D" TextWrapping="Wrap"
                         Text="DriverName - اسم السائق | NationalId - رقم البطاقة | VehicleType - نوع السيارة | VehicleNumber - رقم اللوحة | StartDate - تاريخ البداية | EndDate - تاريخ النهاية | DaysCount - عدد الأيام | TotalPrice - إجمالي السعر | VisitConductor - منفذ الزيارة"/>
                <TextBlock FontSize="11" Foreground="#17A2B8" TextWrapping="Wrap" Margin="0,5,0,0" FontWeight="Bold"
                         Text="🆕 متغيرات جديدة للمبالغ: {TotalPriceFormatted} - المبلغ منسق (35,200.00) | {TotalPriceArabic} - المبلغ بالنص العربي مع الهللات"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
