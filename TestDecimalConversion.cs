using System;
using DriverManagementSystem.Helpers;

namespace DriverManagementSystem
{
    /// <summary>
    /// اختبار دالة تحويل الأرقام مع الكسور العشرية
    /// </summary>
    public class TestDecimalConversion
    {
        public static void TestAmountConversion()
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("=== اختبار دالة تحويل الأرقام مع الكسور العشرية ===");
            Console.WriteLine();

            // اختبار الرقم 35200 كما في الصورة
            Console.WriteLine("🔍 اختبار الرقم 35200 بصيغ مختلفة:");
            Console.WriteLine($"35200 = {NumberToArabicTextHelper.ConvertAmountToArabicText(35200)}");
            Console.WriteLine($"35200.00 = {NumberToArabicTextHelper.ConvertAmountToArabicText(35200.00m)}");
            Console.WriteLine($"35200.50 = {NumberToArabicTextHelper.ConvertAmountToArabicText(35200.50m)}");
            Console.WriteLine($"35200.75 = {NumberToArabicTextHelper.ConvertAmountToArabicText(35200.75m)}");
            Console.WriteLine();

            // اختبار التنسيق الرقمي
            Console.WriteLine("📊 اختبار التنسيق الرقمي:");
            Console.WriteLine($"35200.00 = {NumberToArabicTextHelper.FormatAmountWithDecimals(35200.00m)}");
            Console.WriteLine($"35200.50 = {NumberToArabicTextHelper.FormatAmountWithDecimals(35200.50m)}");
            Console.WriteLine($"35200.75 = {NumberToArabicTextHelper.FormatAmountWithDecimals(35200.75m)}");
            Console.WriteLine();

            // اختبار التنسيق المختلط
            Console.WriteLine("🎯 اختبار التنسيق المختلط (رقم + نص عربي):");
            Console.WriteLine($"35200.00 = {NumberToArabicTextHelper.ConvertToMixedFormat(35200.00m)}");
            Console.WriteLine($"35200.50 = {NumberToArabicTextHelper.ConvertToMixedFormat(35200.50m)}");
            Console.WriteLine($"35200.75 = {NumberToArabicTextHelper.ConvertToMixedFormat(35200.75m)}");
            Console.WriteLine();

            // اختبارات إضافية مع أرقام مختلفة
            Console.WriteLine("🧪 اختبارات إضافية:");
            var testAmounts = new decimal[] { 
                1000.25m, 2500.50m, 5000.75m, 10000.00m, 15000.33m, 20000.99m
            };

            foreach (var amount in testAmounts)
            {
                Console.WriteLine($"{amount} = {NumberToArabicTextHelper.ConvertAmountToArabicText(amount)}");
            }

            Console.WriteLine();
            Console.WriteLine("✅ انتهى الاختبار بنجاح!");
        }

        public static void Main()
        {
            TestAmountConversion();
            Console.WriteLine();
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}
