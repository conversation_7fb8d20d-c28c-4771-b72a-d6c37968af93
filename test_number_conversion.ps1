# اختبار دالة تحويل الأرقام إلى النص العربي

Write-Host "=== اختبار دالة تحويل الأرقام إلى النص العربي ===" -ForegroundColor Green
Write-Host ""

# تحليل الرقم 35200
$number = 35200
Write-Host "الرقم المطلوب تحويله: $number" -ForegroundColor Yellow
Write-Host ""

# تحليل الرقم
$thousands = [Math]::Floor($number / 1000)
$remainder = $number % 1000

Write-Host "تحليل الرقم:" -ForegroundColor Cyan
Write-Host "  الآلاف: $thousands" 
Write-Host "  الباقي: $remainder"
Write-Host ""

# النتيجة المتوقعة
Write-Host "النتيجة المتوقعة:" -ForegroundColor Magenta
Write-Host "  35 ألف = خمسة وثلاثون ألف"
Write-Host "  200 = مائتان"
Write-Host "  الإجمالي: خمسة وثلاثون ألف ومائتان"
Write-Host "  مع العملة: خمسة وثلاثون ألف ومائتان ريال يمني"
Write-Host ""

Write-Host "هذا هو كيف تعمل الدالة NumberToArabicTextHelper.ConvertAmountToArabicText(35200)" -ForegroundColor Green
