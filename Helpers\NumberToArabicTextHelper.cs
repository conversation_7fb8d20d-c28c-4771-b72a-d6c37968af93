using System;

namespace DriverManagementSystem.Helpers
{
    /// <summary>
    /// مساعد تحويل الأرقام إلى نص عربي احترافي
    /// </summary>
    public static class NumberToArabicTextHelper
    {
        private static readonly string[] ones = {
            "", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة",
            "عشرة", "أحد عشر", "اثنا عشر", "ثلاثة عشر", "أربعة عشر", "خمسة عشر", "ستة عشر", "سبعة عشر", "ثمانية عشر", "تسعة عشر"
        };

        private static readonly string[] tens = {
            "", "", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون"
        };

        private static readonly string[] hundreds = {
            "", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة"
        };

        /// <summary>
        /// تحويل رقم إلى نص عربي احترافي
        /// </summary>
        /// <param name="number">الرقم المراد تحويله</param>
        /// <returns>النص العربي للرقم</returns>
        public static string ConvertToArabicText(decimal number)
        {
            try
            {
                if (number == 0)
                    return "صفر";

                if (number < 0)
                    return "سالب " + ConvertToArabicText(-number);

                // تحويل إلى عدد صحيح (تجاهل الكسور)
                long intNumber = (long)number;

                if (intNumber == 0)
                    return "صفر";

                string result = "";

                // المليارات
                if (intNumber >= 1000000000)
                {
                    long billions = intNumber / 1000000000;
                    string billionText = ConvertHundreds((int)billions);
                    if (!string.IsNullOrEmpty(billionText))
                    {
                        result += billionText + " مليار";
                        intNumber %= 1000000000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // الملايين
                if (intNumber >= 1000000)
                {
                    long millions = intNumber / 1000000;
                    string millionText = ConvertHundreds((int)millions);
                    if (!string.IsNullOrEmpty(millionText))
                    {
                        result += millionText + " مليون";
                        intNumber %= 1000000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // الآلاف
                if (intNumber >= 1000)
                {
                    long thousands = intNumber / 1000;
                    string thousandText = ConvertHundreds((int)thousands);
                    if (!string.IsNullOrEmpty(thousandText))
                    {
                        if (thousands == 1)
                            result += "ألف";
                        else if (thousands == 2)
                            result += "ألفان";
                        else if (thousands <= 10)
                            result += thousandText + " آلاف";
                        else
                            result += thousandText + " ألف";

                        intNumber %= 1000;
                        if (intNumber > 0) result += " و";
                    }
                }

                // المئات والعشرات والآحاد
                if (intNumber > 0)
                {
                    string remainderText = ConvertHundreds((int)intNumber);
                    if (!string.IsNullOrEmpty(remainderText))
                    {
                        result += remainderText;
                    }
                }

                return result.Trim();
            }
            catch
            {
                return "خطأ في التحويل";
            }
        }

        /// <summary>
        /// تحويل الأرقام من 1 إلى 999 بطريقة احترافية
        /// </summary>
        private static string ConvertHundreds(int number)
        {
            if (number == 0) return "";

            string result = "";

            // المئات
            if (number >= 100)
            {
                int hundredsDigit = number / 100;
                result += hundreds[hundredsDigit];
                number %= 100;

                // إضافة "و" إذا كان هناك باقي
                if (number > 0)
                    result += " و";
            }

            // العشرات والآحاد
            if (number >= 20)
            {
                int tensDigit = number / 10;
                int onesDigit = number % 10;

                if (onesDigit > 0)
                    result += ones[onesDigit] + " و" + tens[tensDigit];
                else
                    result += tens[tensDigit];
            }
            else if (number > 0)
            {
                result += ones[number];
            }

            return result.Trim();
        }

        /// <summary>
        /// تنسيق المبلغ مع الفواصل والكسور العشرية
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق مع الفواصل (مثل: 35,200.00)</returns>
        public static string FormatAmountWithDecimals(decimal amount)
        {
            try
            {
                // استخدام N2 لإضافة الفواصل مع منزلتين عشريتين
                return amount.ToString("N2", System.Globalization.CultureInfo.InvariantCulture);
            }
            catch
            {
                return amount.ToString("F2");
            }
        }

        /// <summary>
        /// تحويل مبلغ مالي إلى نص عربي مع دعم الهللات
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>النص العربي للمبلغ مع الهللات</returns>
        public static string ConvertAmountToArabicText(decimal amount)
        {
            try
            {
                if (amount == 0)
                    return "صفر ريال يمني";

                // فصل الجزء الصحيح عن الكسري
                long riyals = (long)Math.Floor(amount);          // الجزء الصحيح (ريالات)
                int halalas = (int)((amount - riyals) * 100);    // الجزء الكسري (هللات)

                // تحويل الجزء الصحيح
                string text = ConvertToArabicText(riyals) + " ريال يمني";

                // إضافة الجزء الكسري إن وجد
                if (halalas > 0)
                {
                    text += " و" + ConvertToArabicText(halalas) + " هللة";
                }

                return text;
            }
            catch
            {
                return "خطأ في تحويل المبلغ";
            }
        }

        /// <summary>
        /// تحويل الأرقام إلى نص عربي (الدالة الرئيسية)
        /// </summary>
        /// <param name="number">الرقم</param>
        /// <returns>النص العربي</returns>
        public static string ConvertSimple(decimal number)
        {
            return ConvertToArabicText(number);
        }

        /// <summary>
        /// اختبار الدالة مع أمثلة
        /// </summary>
        public static void TestConversion()
        {
            System.Diagnostics.Debug.WriteLine("=== اختبار تحويل الأرقام ===");
            System.Diagnostics.Debug.WriteLine($"103500 = {ConvertToArabicText(103500)}");
            System.Diagnostics.Debug.WriteLine($"20000 = {ConvertToArabicText(20000)}");
            System.Diagnostics.Debug.WriteLine($"50000 = {ConvertToArabicText(50000)}");
            System.Diagnostics.Debug.WriteLine($"75000 = {ConvertToArabicText(75000)}");
            System.Diagnostics.Debug.WriteLine($"125000 = {ConvertToArabicText(125000)}");
            System.Diagnostics.Debug.WriteLine($"1500 = {ConvertToArabicText(1500)}");
            System.Diagnostics.Debug.WriteLine($"2500 = {ConvertToArabicText(2500)}");
            System.Diagnostics.Debug.WriteLine("=========================");
        }
    }
}
